.PHONY: help install backend frontend tunnel

.DEFAULT_GOAL := help
PROJECT_NAME := paykka-duty

help: ## Show this help message
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

init: ## Initialize project
	@echo "🚀 Initializing project..."
	@docker compose up -d
	@docker compose exec -it db sh -c "createdb -U postgres paykka-duty || true"
	@uv sync
	@uv run manage.py migrate
	@uv run manage.py tailwind setup

clear: ## Clear database
	@echo "🔥 Clearing database..."
	@docker compose down -v

dev: ## Run Django server in development mode
	@echo "🚀 Starting Django server..."
	@uv run manage.py tailwind runserver 0.0.0.0:8000

lint: ## Lint code with Ruff
	@echo "🔍 Linting code with Ruff..."
	@uvx ruff check --fix

tunnel: ## Run cloudflared tunnel
	@echo "🚇 Starting cloudflared tunnel..."
	@cloudflared tunnel run --token `cloudflared tunnel token $(PROJECT_NAME)` --protocol http2
